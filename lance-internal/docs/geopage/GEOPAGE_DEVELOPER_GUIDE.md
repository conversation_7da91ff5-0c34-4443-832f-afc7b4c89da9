# GeoPage Developer Guide

**Target Audience**: Developers working on or contributing to the GeoPage spatial encoding implementation
**Prerequisites**: Familiarity with Rust, Lance architecture, and basic spatial concepts
**Last Updated**: 2025-07-21

> **⚠️ Important**: This implementation has known thread safety issues and unvalidated performance claims. See [Known Issues](#-known-issues--limitations) section before production use.

## 🎯 **What is GeoPage?**

GeoPage is a **spatial-aware physical encoding** for Lance that optimizes storage and query performance for geospatial data. It implements:

- **Quadtree spatial indexing** for efficient spatial queries
- **Z-order (Morton curve) sorting** for improved spatial locality
- **WKB geometry parsing** with support for Points, LineStrings, and Polygons
- **Spatial filter pushdown** integration with <PERSON>'s query pipeline

## 🏗️ **Architecture Overview**

### Core Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GeoPageEncoder│    │ GeoPageScheduler│    │  GeoPageDecoder │
│                 │    │                 │    │                 │
│ • Z-order sort  │    │ • Spatial index │    │ • Data blocks   │
│ • Quadtree gen  │    │ • Filter pushdn │    │ • Geometry parse│
│ • WKB parsing   │    │ • Page pruning  │    │ • Coord extract │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Lance Integration│
                    │                 │
                    │ • ArrayEncoder  │
                    │ • PageScheduler │
                    │ • FieldScheduler│
                    └─────────────────┘
```

### Data Flow

```
Write Path:
RecordBatch → extract_spatial_coords() → z_order_sort() → 
generate_quadtree() → encode_to_pages() → Lance storage

Read Path:
Spatial query → load_quadtree() → filter_pages() → 
decode_blocks() → extract_geometries() → Results
```

## 📁 **Key Implementation Files**

### Core Implementation
| File | Lines | Purpose | Status |
|------|-------|---------|--------|
| `rust/lance-encoding/src/encodings/geopage.rs` | 2,213 | Main GeoPage implementation | ✅ Complete |
| `rust/lance-encoding/src/encodings/wkb_utils.rs` | 418 | WKB geometry parsing | ✅ Production ready |
| `rust/lance/src/io/exec/spatial_index.rs` | 167 | Execution integration | ✅ Functional |

### Integration Points
| File | Changes | Purpose | Risk Level |
|------|---------|---------|------------|
| `rust/lance-encoding/src/encoder.rs` | +95 lines | Encoder selection | Medium |
| `rust/lance/src/dataset/scanner.rs` | +165 lines | Spatial filtering | High |
| `rust/lance-encoding/src/decoder.rs` | +12 lines | Decoder integration | Low |

### Testing & Validation
| File | Lines | Purpose |
|------|-------|---------|
| `rust/lance-encoding/src/encodings/geopage/geopage_validation.rs` | 216 | Validation tests |
| `rust/lance-encoding/src/encodings/wkb_test.rs` | 70 | WKB unit tests |

## 🚀 **Getting Started**

### 1. Build Setup

```bash
# Clone the repository
git clone <your-fork-url>
cd lance-internal

# Switch to the GeoPage branch
git checkout wkb_support

# Build with spatial features
cargo build --features spatial

# Run tests
cargo test geopage
cargo test wkb
```

### 2. Basic Usage

```python
import lance
import pandas as pd

# Create spatial dataset
df = pd.DataFrame({
    'longitude': [-122.4, -122.3, -122.2],
    'latitude': [37.8, 37.7, 37.6],
    'data': ['A', 'B', 'C']
})

# Write with GeoPage encoding
dataset = lance.write_dataset(
    df, 
    "spatial_data.lance",
    encoding="geopage"  # Triggers GeoPage encoder
)

# Query with spatial filter
results = dataset.to_table(
    filter="longitude > -122.35 AND latitude > 37.75"
)
```

### 3. Running Tests

```bash
# Unit tests
cargo test test_geopage_encoder_creation
cargo test test_spatial_utilities
cargo test test_wkb_parsing

# Integration tests
python debug_geopage.py
python test_uber_wkb_integration.py

# Performance tests
python run_comprehensive_wkb_tests.py
```

## 🔧 **Development Workflow**

### Adding New Features

1. **Understand the Architecture**
   - Read `geopage.rs` main implementation
   - Review Lance's `ArrayEncoder`/`PageScheduler` patterns
   - Check existing tests for examples

2. **Make Changes**
   - Follow Rust best practices
   - Use Lance's error handling patterns (`Result<>`)
   - Add comprehensive tests

3. **Test Thoroughly**
   - Unit tests for new functions
   - Integration tests with real data
   - Performance validation

### Code Style Guidelines

```rust
// Good: Follow Lance patterns
impl ArrayEncoder for GeoPageEncoder {
    fn encode(&self, data: DataBlock, _data_type: &DataType, _buffer_index: &mut u32) 
        -> Result<EncodedArray> {
        // Implementation
    }
}

// Good: Proper error handling
fn parse_coordinates(&self, data: &[u8]) -> Result<Vec<SpatialPoint>> {
    if data.is_empty() {
        return Err(Error::InvalidInput {
            source: "Empty coordinate data".into(),
            location: location!(),
        });
    }
    // Implementation
}

// Bad: Global state (current issue)
static SPATIAL_FILTER_CONTEXT: Mutex<Option<(f64, f64, f64, f64)>> = Mutex::new(None);
```

## 🐛 **Known Issues & Limitations**

### 🔴 **Critical Issues**

#### 1. Thread Safety Violation
**Location**: `geopage.rs:42-59`  
**Issue**: Global spatial filter context  
**Impact**: Not safe for concurrent queries  
**Fix**: Pass spatial context through execution plan

```rust
// Current (UNSAFE)
static SPATIAL_FILTER_CONTEXT: Mutex<Option<(f64, f64, f64, f64)>> = Mutex::new(None);

// Proposed fix
impl GeoPageFieldScheduler {
    fn new_with_spatial_filter(spatial_filter: Option<BBox>) -> Self
}
```

#### 2. Performance Unvalidated
**Issue**: Claims of "2-10× speedup" not supported by test data  
**Evidence**: Test results show 3.3% performance degradation  
**Fix**: Run controlled benchmarks, validate actual benefits

### ⚠️ **Medium Issues**

#### 1. Scanner Integration Complexity
**Location**: `scanner.rs` (+165 lines)  
**Issue**: Complex modifications to core query path  
**Risk**: Potential regressions in non-spatial queries

#### 2. Limited DataFusion Integration
**Issue**: Custom filter parsing instead of DataFusion integration  
**Impact**: Misses query optimization opportunities

### 🟡 **Minor Issues**

#### 1. Hardcoded Constants
```rust
const GEO_PAGE_SIZE: usize = 4096;
const DEFAULT_QUADTREE_ZOOM_LEVEL: u32 = 12;
```

#### 2. Code Organization
**Issue**: Single 2,213-line file  
**Suggestion**: Split into modules

## 🧪 **Testing Strategy**

### Unit Tests
```bash
# Core functionality
cargo test test_geopage_encoder_creation
cargo test test_spatial_utilities
cargo test test_quadtree_operations

# WKB parsing
cargo test test_wkb_point_parsing
cargo test test_wkb_linestring_parsing
cargo test test_wkb_polygon_parsing
```

### Integration Tests
```bash
# Python integration
python debug_geopage.py
python diagnose_geopage.py

# Real-world data
python test_uber_wkb_integration.py --max-rows 100000
```

### Performance Tests
```bash
# Comprehensive benchmarking
python run_comprehensive_wkb_tests.py --modes all
python run_comprehensive_wkb_ab_tests.py
```

## 📊 **Performance Considerations**

### Current Performance Profile

**Strengths**:
- Spatial filtering works functionally
- Z-order sorting improves spatial locality (theoretical)
- Quadtree indexing enables page pruning

**Weaknesses**:
- Overhead from spatial optimizations
- Memory usage during Z-order sorting (2× memory)
- Complex encoding/decoding path

### Optimization Opportunities

1. **Memory Efficiency**: Reduce memory overhead during sorting
2. **Filter Integration**: Integrate with DataFusion's filter pushdown
3. **Lazy Loading**: Defer quadtree loading until needed
4. **Parallel Processing**: Parallelize spatial operations

## 🔍 **Debugging Tips**

### Common Issues

#### 1. Encoding Not Triggered
```python
# Check field metadata
dataset = lance.write_dataset(df, "test.lance", encoding="geopage")
# Verify in logs: "GeoPageEncoder: Creating GeoPage encoding"
```

#### 2. Spatial Filtering Not Working
```rust
// Check spatial filter context
println!("Spatial filter: {:?}", get_spatial_filter_context());
```

#### 3. WKB Parsing Errors
```rust
// Enable WKB debugging
if SimpleWkbParser::is_likely_wkb(geometry_bytes) {
    println!("WKB detected, parsing...");
}
```

### Useful Debug Commands

```bash
# Check git changes
git diff main..wkb_support --stat

# Find GeoPage usage
grep -r "geopage" rust/lance-encoding/src/

# Check test results
ls -la test_results/comprehensive_wkb_*.json
```

## 📚 **Additional Resources**

### Documentation
- `docs/FILE_CHANGES_INVENTORY.md` - Complete change tracking
- `docs/WKB_IMPLEMENTATION_GUIDE.md` - WKB geometry support
- `docs/TECHNICAL_AUDIT_REPORT.md` - Comprehensive technical analysis
- `docs/IMPLEMENTATION_STATUS_REPORT.md` - Detailed implementation status
- `docs/ARCHITECTURE_REVIEW.md` - Architecture and design decisions

### External References
- [Lance Documentation](https://lancedb.github.io/lance/)
- [WKB Specification](https://www.ogc.org/standards/wkb)
- [Quadtree Indexing](https://en.wikipedia.org/wiki/Quadtree)
- [Z-order Curve](https://en.wikipedia.org/wiki/Z-order_curve)

## 🤝 **Contributing**

### Before Contributing
1. Read this guide thoroughly
2. Review the technical audit documents
3. Run the test suite to ensure everything works
4. Understand the known limitations

### Contribution Priorities
1. **High Priority**: Fix thread safety issues
2. **Medium Priority**: Performance validation and optimization
3. **Low Priority**: Code organization and documentation

### Getting Help
- Review existing tests for examples
- Check the technical audit documents for detailed analysis
- Look at Lance's existing encodings for patterns
