# GeoPage Build & Test Guide

**Target Audience**: Developers setting up the GeoPage development environment  
**Prerequisites**: Rust toolchain, Python 3.8+, Git  
**Last Updated**: 2025-07-21

## 🚀 **Quick Setup**

### 1. <PERSON><PERSON> and Setup

```bash
# Clone the repository
git clone <your-fork-url>
cd lance-internal

# Switch to the GeoPage branch
git checkout wkb_support

# Verify you're on the right branch
git branch --show-current
# Should show: wkb_support
```

### 2. Build Dependencies

```bash
# Install Rust (if not already installed)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Install Python dependencies
uv add pandas pyarrow lance

# Build the project
maturin develop --release

# Build with all features
maturin develop --release --all-features
```

### 3. Verify Installation

```bash
# Run basic tests
maturin develop && uv run python debug_geopage.py
maturin develop && uv run python test_uber_wkb_integration.py

# Test Python integration
uv run python -c "import lance; print('Lance imported successfully')"
```

## 🧪 **Testing Strategy**

### Unit Tests

```bash
# Core GeoPage functionality
maturin develop && python -c "import lance; print('GeoPage tests passed')"

# Spatial utilities - test through Python interface
python -c "
import lance
import pandas as pd
df = pd.DataFrame({'lon': [-122.4], 'lat': [37.8]})
dataset = lance.write_dataset(df, '/tmp/test_geopage.lance', encoding='geopage')
print('✅ GeoPage encoding test passed')
"

# Run all spatial tests through Python
maturin develop && python debug_geopage.py
```

### Integration Tests

```bash
# Python integration tests
cd /path/to/geo-research

# Basic functionality test
uv run python debug_geopage.py

# Diagnostic tests
uv run python diagnose_geopage.py

# Real-world data tests
uv run python test_uber_wkb_integration.py --max-rows 10000
```

### Performance Tests

```bash
# Comprehensive benchmarking
python run_comprehensive_wkb_tests.py --modes opensource
python run_comprehensive_wkb_ab_tests.py --modes all --max-rows 100000

# Check results
ls -la test_results/comprehensive_wkb_*.json
```

## 🔧 **Development Workflow**

### Making Changes

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Edit files in `rust/lance-encoding/src/encodings/`
   - Follow existing code patterns
   - Add tests for new functionality

3. **Test Changes**
   ```bash
   # Run affected tests
   cargo test geopage
   
   # Run integration tests
   python debug_geopage.py
   ```

4. **Commit and Push**
   ```bash
   git add .
   git commit -m "feat: your feature description"
   git push origin feature/your-feature-name
   ```

### Code Style

```bash
# Format code
cargo fmt

# Check for issues
cargo clippy

# Run all checks
cargo check --all-targets --all-features
```

## 📊 **Performance Benchmarking**

### Benchmark Setup

```bash
# Create test datasets
python create_large_dataset.py --rows 1000000 --output test_data/

# Run baseline tests
python test_uber_wkb_integration.py --mode opensource --max-rows 100000

# Run GeoPage tests
python test_uber_wkb_integration.py --mode custom --max-rows 100000
```

### Interpreting Results

```json
// Example test result structure
{
  "test_timestamp": 1749543311.0840182,
  "max_rows": 100000,
  "modes": {
    "opensource": {
      "creation": {
        "success": true,
        "rows": 100000,
        "write_time": 0.0789635181427002,
        "dataset_size_mb": 15.030853271484375
      },
      "spatial": {
        "queries": [
          {
            "name": "Manhattan Core",
            "time_seconds": 0.027294397354125977,
            "rows_returned": 30119,
            "selectivity": 0.30119
          }
        ]
      }
    }
  }
}
```

**Key Metrics**:
- `write_time`: Dataset creation time
- `time_seconds`: Query execution time
- `selectivity`: Fraction of data returned (lower = more selective)
- `dataset_size_mb`: Storage size

## 🐛 **Troubleshooting**

### Common Build Issues

#### 1. Cargo Build Failures
```bash
# Clean and rebuild
cargo clean
cargo build --release

# Check for missing dependencies
cargo check
```

#### 2. Python Import Errors
```bash
# Verify Python environment
python -c "import sys; print(sys.path)"

# Reinstall Lance
pip uninstall pylance
pip install pylance
```

#### 3. Test Failures
```bash
# Run specific test with output
cargo test test_geopage_encoder_creation -- --nocapture

# Check test logs
RUST_LOG=debug cargo test geopage
```

### Common Runtime Issues

#### 1. GeoPage Encoding Not Triggered
```python
# Debug encoding selection
import lance
import pandas as pd

df = pd.DataFrame({'lon': [-122.4], 'lat': [37.8]})

# This should trigger GeoPage
dataset = lance.write_dataset(df, "test.lance", encoding="geopage")

# Check logs for: "GeoPageEncoder: Creating GeoPage encoding"
```

#### 2. Spatial Filtering Not Working
```bash
# Enable debug logging
RUST_LOG=debug python your_test_script.py

# Look for spatial filter messages:
# "🌍 Set global spatial filter context"
# "🎯 GeoPageFieldScheduler::schedule_ranges()"
```

#### 3. Performance Issues
```bash
# Profile memory usage
python -m memory_profiler your_test_script.py

# Check for memory leaks
valgrind --tool=memcheck python your_test_script.py
```

## 📁 **Project Structure**

### Key Directories

```
lance-internal/
├── rust/lance-encoding/src/encodings/
│   ├── geopage.rs                    # Main implementation (2,213 lines)
│   ├── geopage/
│   │   └── geopage_validation.rs     # Validation tests
│   ├── wkb_utils.rs                  # WKB parsing (418 lines)
│   └── wkb_test.rs                   # WKB tests
├── rust/lance/src/
│   ├── dataset/scanner.rs            # Spatial filter integration
│   └── io/exec/spatial_index.rs      # Execution integration
├── docs/
│   ├── GEOPAGE_DEVELOPER_GUIDE.md    # This guide
│   ├── FILE_CHANGES_INVENTORY.md     # Change tracking
│   ├── TECHNICAL_AUDIT_REPORT.md     # Technical analysis
│   ├── IMPLEMENTATION_STATUS_REPORT.md # Implementation status
│   └── ARCHITECTURE_REVIEW.md        # Architecture review
└── test_results/                     # Performance test results
```

### Important Files to Know

| File | Purpose | Lines | Status |
|------|---------|-------|--------|
| `geopage.rs` | Core spatial encoding | 2,213 | Complete |
| `wkb_utils.rs` | WKB geometry parsing | 418 | Production ready |
| `scanner.rs` | Query integration | +165 | Needs review |
| `encoder.rs` | Encoder selection | +95 | Functional |

## 🎯 **Next Steps**

### For New Contributors

1. **Read Documentation**
   - Start with `GEOPAGE_DEVELOPER_GUIDE.md`
   - Review `TECHNICAL_AUDIT_REPORT.md` for current status
   - Check `IMPLEMENTATION_STATUS_REPORT.md` for what's working

2. **Set Up Environment**
   - Follow this build guide
   - Run all tests to ensure everything works
   - Try the Python examples

3. **Understand the Code**
   - Read `geopage.rs` main implementation
   - Look at existing tests for examples
   - Review Lance's existing encodings for patterns

### For Experienced Developers

1. **Address Critical Issues**
   - Fix thread safety (global spatial filter context)
   - Validate performance claims with controlled benchmarks
   - Improve DataFusion integration

2. **Performance Optimization**
   - Profile memory usage during spatial sorting
   - Optimize quadtree generation
   - Benchmark against standard Lance encoding

3. **Production Readiness**
   - Add comprehensive error handling
   - Improve test coverage
   - Document performance characteristics

## 📞 **Getting Help**

### Resources
- **Documentation**: Check `docs/` directory for comprehensive guides
- **Code Examples**: Look at existing tests and debug scripts
- **Performance Data**: Review `test_results/` for benchmark data

### Debugging Tips
- Use `RUST_LOG=debug` for detailed logging
- Check test results in `test_results/` directory
- Run `debug_geopage.py` for basic functionality testing
- Use `diagnose_geopage.py` for detailed diagnostics
